{"name": "ticket-system-fullstack", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint-fix": "next lint --fix", "type": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "next": "15.1.8", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "eslint-plugin-simple-import-sort": "^12.1.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}